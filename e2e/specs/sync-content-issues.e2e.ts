import { chromium } from 'playwright';
import type { <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';
import { describe, test, expect, beforeAll, beforeEach, afterAll } from 'vitest';
import { setupGhostAPIMocking, shouldRecordGhostAPI } from '../fixtures/ghost-api-helper';
import { registerPageForUIReset } from '../helpers/test-setup';
import fs from 'fs';
import * as path from 'path';

/**
 * Wait for async operations to complete with smart polling
 */
async function waitForAsyncOperation(timeout: number = 1000): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, timeout));
}

/**
 * Helper to create a test file with specific content using Obsidian's vault API
 */
async function createTestFile(page: Page, filePath: string, content: string): Promise<void> {
  await page.evaluate(async ({ path, fileContent }) => {
    try {
      // Create the file using Obsidian's vault API
      await (window as any).app.vault.create(path, fileContent);
      return true;
    } catch (error) {
      console.error('Error creating test file:', error);
      throw error;
    }
  }, { path: filePath, fileContent: content });
}

/**
 * E2E Tests for Sync Content Issues
 *
 * These tests reproduce the reported sync issues:
 * 1. Local changes of content are not stored in Ghost
 * 2. Local content gets overridden by content from Ghost
 * 3. Visibility is always displayed as unsynced even though its value is correctly set
 */

describe("Sync Content Issues E2E Tests", () => {
  let browser: Browser;
  let page: Page;
  const articlesDir = path.join(process.cwd(), 'e2e/test_obsidian_data/vault/articles');

  beforeAll(async () => {
    // Connect to existing Obsidian instance via CDP
    browser = await chromium.connectOverCDP('http://127.0.0.1:9222');

    // Get the first page (Obsidian window)
    const contexts = browser.contexts();
    const context = contexts[0];
    const pages = context.pages();
    page = pages[0];

    console.log("Connected to Obsidian via Playwright");

    // Set up Ghost API mocking for sync content issues scenarios
    await setupGhostAPIMocking(page, context, {
      ghostUrl: 'https://solnic.ghost.io',
      scenario: 'sync-content-issues',
      record: shouldRecordGhostAPI()
    });

    // Enable the ghost-sync plugin
    await page.evaluate(() => {
      (window as any).app.plugins.setEnable(true);
      (window as any).app.plugins.enablePlugin('ghost-sync');
    });

    // Wait for plugin to be ready
    await waitForAsyncOperation(1000);

    // Register page for global UI reset
    registerPageForUIReset(page);
  });

  beforeEach(async () => {
    // Clear any existing test files
    if (fs.existsSync(articlesDir)) {
      const files = fs.readdirSync(articlesDir);
      for (const file of files) {
        if (file.includes('sync-content-test') && file.endsWith('.md')) {
          fs.unlinkSync(path.join(articlesDir, file));
        }
      }
    }

    // Wait for file system operations to complete
    await waitForAsyncOperation(500);
  });

  afterAll(async () => {
    if (browser) {
      await browser.close();
    }
  });

  test("should reproduce issue: local content changes not synced to Ghost", async () => {
    const testSlug = "sync-content-test-local-changes";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Step 1: Create initial post with specific content
    const initialContent = `---
title: "Sync Content Test - Local Changes"
slug: "${testSlug}"
status: "draft"
visibility: "public"
---

# Initial Content

This is the initial content that should be synced to Ghost.

## Initial Section

Some initial text here.`;

    await createTestFile(page, relativeFilePath, initialContent);
    console.log(`Created initial test file: ${relativeFilePath}`);

    // Step 2: Simulate first sync to Ghost (create the post)
    await page.evaluate((path) => {
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (file) {
        (window as any).app.workspace.getLeaf().openFile(file);
      }
    }, relativeFilePath);

    await waitForAsyncOperation(1000);

    // Trigger sync to Ghost to create the post
    await page.evaluate(() => {
      (window as any).app.commands.executeCommandById('ghost-sync:sync-current-to-ghost');
    });

    await waitForAsyncOperation(3000);

    // Step 3: Modify the local content significantly
    const modifiedContent = `---
title: "Sync Content Test - Local Changes"
slug: "${testSlug}"
status: "draft"
visibility: "public"
---

# MODIFIED Content - This Should Sync to Ghost

This content has been SIGNIFICANTLY MODIFIED locally and should be synced to Ghost.

## NEW Section Added Locally

This is completely new content added locally.

### Subsection with Important Changes

- New bullet point 1
- New bullet point 2
- Critical information that must be preserved

The original content has been replaced with this new content.`;

    // Update the file content
    await page.evaluate(async ({ path, content }) => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath(path);
      if (file) {
        await app.vault.modify(file, content);
        console.log('Modified file content locally');
      }
    }, { path: relativeFilePath, content: modifiedContent });

    await waitForAsyncOperation(1000);

    // Step 4: Mark the file as changed to simulate local modifications
    await page.evaluate((path) => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (plugin && file && plugin.syncMetadata) {
        return plugin.syncMetadata.markAsChanged(file);
      }
    }, relativeFilePath);

    await waitForAsyncOperation(500);

    // Step 5: Trigger sync again - this should sync local changes TO Ghost
    await page.evaluate(() => {
      (window as any).app.commands.executeCommandById('ghost-sync:sync-current-to-ghost');
    });

    await waitForAsyncOperation(3000);

    // Step 6: Verify that local content was preserved (not overridden)
    const finalContent = await page.evaluate((path) => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath(path);
      if (file) {
        return app.vault.read(file);
      }
      return null;
    }, relativeFilePath);

    console.log("Final content after sync:", finalContent);

    // ASSERTION: The local modifications should be preserved
    expect(finalContent).toContain("MODIFIED Content - This Should Sync to Ghost");
    expect(finalContent).toContain("NEW Section Added Locally");
    expect(finalContent).toContain("Critical information that must be preserved");
    expect(finalContent).not.toContain("This is the initial content");

    console.log("✅ Local content changes were preserved during sync");
  });

  test("should reproduce issue: visibility always shows as unsynced", async () => {
    const testSlug = "sync-content-test-visibility";
    const relativeFilePath = `articles/${testSlug}.md`;

    // Create post with specific visibility setting
    const content = `---
title: "Sync Content Test - Visibility"
slug: "${testSlug}"
status: "draft"
visibility: "members"
---

# Visibility Test

This post should have visibility set to "members" and should show as synced.`;

    await createTestFile(page, relativeFilePath, content);
    console.log(`Created test file with visibility: ${relativeFilePath}`);

    // Open the file
    await page.evaluate((path) => {
      const file = (window as any).app.vault.getAbstractFileByPath(path);
      if (file) {
        (window as any).app.workspace.getLeaf().openFile(file);
      }
    }, relativeFilePath);

    await waitForAsyncOperation(1000);

    // Sync to Ghost first
    await page.evaluate(() => {
      (window as any).app.commands.executeCommandById('ghost-sync:sync-current-to-ghost');
    });

    await waitForAsyncOperation(3000);

    // Check sync status for visibility
    const syncStatus = await page.evaluate(() => {
      const plugin = (window as any).app.plugins.plugins['ghost-sync'];
      if (plugin && plugin.syncStatusService) {
        const activeFile = (window as any).app.workspace.getActiveFile();
        if (activeFile) {
          return plugin.syncStatusService.calculateSyncStatus(activeFile);
        }
      }
      return null;
    });

    console.log("Sync status for visibility test:", syncStatus);

    // ASSERTION: Visibility should show as synced when values match
    if (syncStatus) {
      expect(syncStatus.visibility).toBe('synced');
      console.log("✅ Visibility shows as synced correctly");
    } else {
      console.log("❌ Could not get sync status - this indicates a problem");
      expect(syncStatus).not.toBeNull();
    }
  });
});
